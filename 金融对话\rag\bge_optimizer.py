"""
BGE-M3模型CPU性能优化工具
提供多种优化策略来提高BGE-M3模型在CPU上的运行效率
"""
import os
import time
import threading
import queue
import psutil
import hashlib
from typing import List, Dict, Any, Optional
from loguru import logger
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed


class BGEPerformanceOptimizer:
    """BGE-M3性能优化器"""
    
    def __init__(self):
        self.text_cache = {}
        self.max_cache_size = 3000
        self.performance_stats = {
            "total_encodings": 0,
            "cache_hits": 0,
            "total_time": 0.0,
            "avg_time": 0.0
        }
        self.lock = threading.Lock()
        
    def setup_environment_optimization(self):
        """设置环境优化参数"""
        try:
            # CPU优化环境变量
            cpu_count = os.cpu_count()
            optimal_threads = min(4, max(1, cpu_count // 2))
            
            # 设置各种线程库的线程数
            os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
            os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
            os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)
            os.environ['OPENBLAS_NUM_THREADS'] = str(optimal_threads)
            os.environ['VECLIB_MAXIMUM_THREADS'] = str(optimal_threads)
            
            # PyTorch特定优化
            try:
                import torch
                torch.set_num_threads(optimal_threads)
                torch.set_num_interop_threads(1)  # 减少线程间开销
                
                # 禁用一些不必要的功能
                torch.backends.cudnn.enabled = False
                torch.backends.mkldnn.enabled = True  # 启用MKL-DNN优化
                
                logger.info(f"环境优化完成 - CPU线程数: {optimal_threads}")
                return True
                
            except ImportError:
                logger.warning("PyTorch未安装，跳过PyTorch优化")
                return True
                
        except Exception as e:
            logger.error(f"环境优化失败: {e}")
            return False
    
    def optimize_model_for_cpu(self, model):
        """优化模型以提高CPU性能"""
        try:
            import torch
            
            # 设置为评估模式
            model.eval()
            
            # 禁用梯度计算
            for param in model.parameters():
                param.requires_grad = False
            
            # 尝试使用JIT编译优化
            try:
                # 对模型的各个组件进行优化
                for module in model.modules():
                    if hasattr(module, 'eval'):
                        module.eval()
                        
                logger.info("模型CPU优化完成")
                return True
                
            except Exception as jit_error:
                logger.debug(f"JIT优化失败: {jit_error}")
                return True  # 即使JIT失败，基本优化仍然有效
                
        except Exception as e:
            logger.error(f"模型优化失败: {e}")
            return False
    
    def preprocess_text_smart(self, text: str) -> str:
        """智能文本预处理"""
        if not text or not text.strip():
            return ""
        
        # 快速清理
        text = text.strip()
        
        # 移除多余的空白字符
        text = ' '.join(text.split())
        
        # BGE-M3最优长度处理
        max_length = 512
        if len(text) > max_length:
            # 智能截断策略
            words = text.split()
            if len(words) > 80:
                # 保留开头和结尾的重要信息
                keep_start = int(len(words) * 0.6)
                keep_end = int(len(words) * 0.4)
                text = ' '.join(words[:keep_start] + ['...'] + words[-keep_end:])
            else:
                text = text[:max_length]
        
        return text
    
    def get_cache_key(self, text: str) -> str:
        """生成缓存键"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def cache_embedding(self, cache_key: str, embedding: List[float]):
        """缓存嵌入向量"""
        with self.lock:
            # 如果缓存满了，删除最旧的条目
            if len(self.text_cache) >= self.max_cache_size:
                # 删除最旧的20%
                items_to_remove = list(self.text_cache.keys())[:self.max_cache_size // 5]
                for key in items_to_remove:
                    del self.text_cache[key]
            
            self.text_cache[cache_key] = embedding
    
    def get_cached_embedding(self, cache_key: str) -> Optional[List[float]]:
        """获取缓存的嵌入向量"""
        with self.lock:
            return self.text_cache.get(cache_key)
    
    def update_performance_stats(self, encoding_time: float, cache_hit: bool):
        """更新性能统计"""
        with self.lock:
            self.performance_stats["total_encodings"] += 1
            if cache_hit:
                self.performance_stats["cache_hits"] += 1
            else:
                self.performance_stats["total_time"] += encoding_time
            
            # 计算平均时间（仅非缓存命中）
            non_cache_encodings = self.performance_stats["total_encodings"] - self.performance_stats["cache_hits"]
            if non_cache_encodings > 0:
                self.performance_stats["avg_time"] = self.performance_stats["total_time"] / non_cache_encodings
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        with self.lock:
            stats = self.performance_stats.copy()
            cache_hit_rate = (stats["cache_hits"] / max(1, stats["total_encodings"])) * 100
            
            return {
                "total_encodings": stats["total_encodings"],
                "cache_hits": stats["cache_hits"],
                "cache_hit_rate": f"{cache_hit_rate:.1f}%",
                "avg_encoding_time": f"{stats['avg_time']:.2f}s",
                "total_encoding_time": f"{stats['total_time']:.2f}s",
                "cache_size": len(self.text_cache),
                "memory_usage": f"{psutil.virtual_memory().percent:.1f}%"
            }
    
    def clear_cache(self):
        """清空缓存"""
        with self.lock:
            self.text_cache.clear()
            logger.info("性能优化器缓存已清空")
    
    def optimize_batch_processing(self, texts: List[str], max_workers: int = 2) -> List[str]:
        """优化批量处理"""
        if not texts:
            return []
        
        # 预处理所有文本
        processed_texts = []
        
        # 使用线程池进行并行预处理（对于大量文本）
        if len(texts) > 100:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_text = {
                    executor.submit(self.preprocess_text_smart, text): text 
                    for text in texts
                }
                
                for future in as_completed(future_to_text):
                    try:
                        processed_text = future.result()
                        processed_texts.append(processed_text)
                    except Exception as e:
                        logger.error(f"文本预处理失败: {e}")
                        processed_texts.append("")
        else:
            # 对于少量文本，直接处理
            processed_texts = [self.preprocess_text_smart(text) for text in texts]
        
        return processed_texts
    
    def monitor_system_resources(self) -> Dict[str, Any]:
        """监控系统资源"""
        try:
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            
            return {
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / (1024**3),
                "cpu_percent": cpu_percent,
                "cache_size": len(self.text_cache),
                "cache_memory_mb": len(self.text_cache) * 4 * 1024 / (1024**2)  # 估算
            }
        except Exception as e:
            logger.error(f"资源监控失败: {e}")
            return {}
    
    def suggest_optimizations(self) -> List[str]:
        """根据当前状态建议优化措施"""
        suggestions = []
        
        try:
            resources = self.monitor_system_resources()
            stats = self.get_performance_report()
            
            # 内存建议
            if resources.get("memory_percent", 0) > 80:
                suggestions.append("内存使用率过高，建议清理缓存或减小批次大小")
            
            # 缓存建议
            cache_hit_rate = float(stats.get("cache_hit_rate", "0").rstrip('%'))
            if cache_hit_rate < 30:
                suggestions.append("缓存命中率较低，考虑增加缓存大小")
            
            # 性能建议
            avg_time = float(stats.get("avg_encoding_time", "0").rstrip('s'))
            if avg_time > 3:
                suggestions.append("编码时间较长，建议减小文本长度或使用更小的批次")
            
            # CPU建议
            if resources.get("cpu_percent", 0) > 90:
                suggestions.append("CPU使用率过高，建议减少并发处理")
            
            if not suggestions:
                suggestions.append("当前性能良好，无需特殊优化")
                
        except Exception as e:
            logger.error(f"生成优化建议失败: {e}")
            suggestions.append("无法生成优化建议")
        
        return suggestions


class BGEBatchProcessor:
    """BGE批量处理器"""
    
    def __init__(self, optimizer: BGEPerformanceOptimizer):
        self.optimizer = optimizer
        self.processing_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
    def process_batch_smart(self, texts: List[str], model, batch_size: int = 4) -> List[List[float]]:
        """智能批量处理"""
        if not texts:
            return []
        
        # 预处理和缓存检查
        processed_texts = []
        cache_keys = []
        cached_results = {}
        texts_to_encode = []
        
        for text in texts:
            processed_text = self.optimizer.preprocess_text_smart(text)
            cache_key = self.optimizer.get_cache_key(processed_text)
            
            processed_texts.append(processed_text)
            cache_keys.append(cache_key)
            
            cached_embedding = self.optimizer.get_cached_embedding(cache_key)
            if cached_embedding:
                cached_results[cache_key] = cached_embedding
            else:
                texts_to_encode.append((processed_text, cache_key))
        
        logger.info(f"缓存命中: {len(cached_results)}/{len(texts)}")
        
        # 批量编码未缓存的文本
        new_embeddings = {}
        if texts_to_encode:
            new_embeddings = self._encode_batch_optimized(
                [item[0] for item in texts_to_encode],
                [item[1] for item in texts_to_encode],
                model,
                batch_size
            )
        
        # 组装结果
        results = []
        for i, cache_key in enumerate(cache_keys):
            if cache_key in cached_results:
                results.append(cached_results[cache_key])
                self.optimizer.update_performance_stats(0, True)
            elif cache_key in new_embeddings:
                embedding = new_embeddings[cache_key]
                results.append(embedding)
                self.optimizer.cache_embedding(cache_key, embedding)
            else:
                results.append([])  # 失败情况
        
        return results
    
    def _encode_batch_optimized(self, texts: List[str], cache_keys: List[str], 
                               model, batch_size: int) -> Dict[str, List[float]]:
        """优化的批量编码"""
        results = {}
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_keys = cache_keys[i:i + batch_size]
            
            try:
                start_time = time.time()
                
                import torch
                with torch.no_grad():
                    embeddings = model.encode(
                        batch_texts,
                        normalize_embeddings=True,
                        batch_size=len(batch_texts),
                        show_progress_bar=False,
                        convert_to_numpy=True
                    )
                
                encoding_time = time.time() - start_time
                avg_time = encoding_time / len(batch_texts)
                
                for key, embedding in zip(batch_keys, embeddings):
                    results[key] = embedding.tolist()
                    self.optimizer.update_performance_stats(avg_time, False)
                
                logger.debug(f"批次编码完成: {len(batch_texts)}个文本, {encoding_time:.2f}s")
                
            except Exception as e:
                logger.error(f"批次编码失败: {e}")
        
        return results
