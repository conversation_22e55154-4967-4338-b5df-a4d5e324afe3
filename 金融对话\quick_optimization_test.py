"""
BGE-M3 CPU优化快速测试
验证优化功能是否正常工作
"""
import sys
from pathlib import Path
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_optimizer_import():
    """测试优化器导入"""
    try:
        from rag.bge_optimizer import BGEPerformanceOptimizer, BGEBatchProcessor
        logger.info("✓ 优化器模块导入成功")
        return True
    except Exception as e:
        logger.error(f"✗ 优化器模块导入失败: {e}")
        return False

def test_rag_system_import():
    """测试RAG系统导入"""
    try:
        from rag.rag_system import RAGSystem
        logger.info("✓ RAG系统模块导入成功")
        return True
    except Exception as e:
        logger.error(f"✗ RAG系统模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from rag.bge_optimizer import BGEPerformanceOptimizer
        
        # 创建优化器
        optimizer = BGEPerformanceOptimizer()
        
        # 测试环境优化设置
        if optimizer.setup_environment_optimization():
            logger.info("✓ 环境优化设置成功")
        else:
            logger.warning("⚠ 环境优化设置失败，但可以继续")
        
        # 测试文本预处理
        test_text = "这是一个测试文本，用于验证预处理功能是否正常工作。" * 20
        processed = optimizer.preprocess_text_smart(test_text)
        
        if len(processed) <= 512 and len(processed) > 0:
            logger.info(f"✓ 文本预处理正常，原长度: {len(test_text)}, 处理后: {len(processed)}")
        else:
            logger.error("✗ 文本预处理异常")
            return False
        
        # 测试缓存功能
        cache_key = optimizer.get_cache_key("测试")
        test_embedding = [0.1] * 1024
        optimizer.cache_embedding(cache_key, test_embedding)
        
        cached = optimizer.get_cached_embedding(cache_key)
        if cached == test_embedding:
            logger.info("✓ 缓存功能正常")
        else:
            logger.error("✗ 缓存功能异常")
            return False
        
        # 测试性能统计
        optimizer.update_performance_stats(1.5, False)
        optimizer.update_performance_stats(0.01, True)
        
        report = optimizer.get_performance_report()
        if "cache_hit_rate" in report:
            logger.info(f"✓ 性能统计正常，缓存命中率: {report['cache_hit_rate']}")
        else:
            logger.error("✗ 性能统计异常")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 基本功能测试失败: {e}")
        return False

def test_rag_integration():
    """测试RAG系统集成"""
    try:
        from rag.rag_system import RAGSystem
        
        # 创建RAG系统实例
        rag_system = RAGSystem()
        
        # 检查优化器是否正确集成
        if hasattr(rag_system, 'optimizer'):
            logger.info("✓ 优化器已集成到RAG系统")
        else:
            logger.error("✗ 优化器未集成到RAG系统")
            return False
        
        # 测试优化器功能
        test_text = "金融科技创新"
        processed = rag_system.optimizer.preprocess_text_optimized(test_text)
        
        if processed:
            logger.info("✓ RAG系统优化器功能正常")
        else:
            logger.error("✗ RAG系统优化器功能异常")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ RAG系统集成测试失败: {e}")
        return False

def test_configuration():
    """测试配置"""
    try:
        from idconfig.config import Config
        
        config = Config()
        
        # 检查关键配置
        if hasattr(config, 'VECTOR_DIM') and config.VECTOR_DIM == 1024:
            logger.info("✓ 向量维度配置正确")
        else:
            logger.warning("⚠ 向量维度配置可能需要调整")
        
        if hasattr(config, 'EMBEDDING_MODEL'):
            logger.info(f"✓ 嵌入模型路径: {config.EMBEDDING_MODEL}")
        else:
            logger.error("✗ 嵌入模型路径未配置")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始BGE-M3 CPU优化快速测试")
    
    tests = [
        ("优化器模块导入", test_optimizer_import),
        ("RAG系统模块导入", test_rag_system_import),
        ("基本功能", test_basic_functionality),
        ("RAG系统集成", test_rag_integration),
        ("配置检查", test_configuration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 测试: {test_name} ---")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✓ {test_name} 通过")
            else:
                logger.error(f"✗ {test_name} 失败")
                
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("快速测试总结")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 快速测试全部通过！优化功能已就绪")
        logger.info("\n下一步:")
        logger.info("1. 运行完整测试: python test_bge_optimization.py")
        logger.info("2. 启动系统验证实际性能")
        logger.info("3. 查看优化指南: docs/BGE_CPU_优化指南.md")
    else:
        logger.warning(f"⚠️ 有 {total - passed} 个测试失败")
        logger.info("\n建议:")
        logger.info("1. 检查模型路径配置")
        logger.info("2. 确认依赖包安装完整")
        logger.info("3. 查看错误日志排查问题")

if __name__ == "__main__":
    main()
