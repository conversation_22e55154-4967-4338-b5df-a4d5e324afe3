"""
BGE-M3 CPU优化测试脚本
测试和演示BGE-M3模型在CPU上的性能优化效果
"""
import time
import sys
from pathlib import Path
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rag.rag_system import RAGSystem
from rag.bge_optimizer import BGEPerformanceOptimizer, BGEBatchProcessor


def test_basic_optimization():
    """测试基本优化功能"""
    logger.info("=== 测试基本优化功能 ===")
    
    # 创建优化器
    optimizer = BGEPerformanceOptimizer()
    
    # 设置环境优化
    if optimizer.setup_environment_optimization():
        logger.info("✓ 环境优化设置成功")
    else:
        logger.error("✗ 环境优化设置失败")
    
    # 测试文本预处理
    test_texts = [
        "这是一个测试文本",
        "这是一个非常长的测试文本，包含了很多重复的内容和信息，用于测试文本预处理功能的效果" * 10,
        "",
        "   多余空格   的   文本   ",
        "简短文本"
    ]
    
    logger.info("测试文本预处理:")
    for i, text in enumerate(test_texts):
        processed = optimizer.preprocess_text_smart(text)
        logger.info(f"原文长度: {len(text)}, 处理后长度: {len(processed)}")
    
    # 测试缓存功能
    cache_key = optimizer.get_cache_key("测试文本")
    test_embedding = [0.1] * 1024
    optimizer.cache_embedding(cache_key, test_embedding)
    
    cached = optimizer.get_cached_embedding(cache_key)
    if cached == test_embedding:
        logger.info("✓ 缓存功能正常")
    else:
        logger.error("✗ 缓存功能异常")


def test_rag_system_optimization():
    """测试RAG系统优化"""
    logger.info("=== 测试RAG系统优化 ===")
    
    try:
        # 初始化RAG系统
        rag_system = RAGSystem()
        
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        logger.info("✓ RAG系统初始化成功")
        
        # 测试单个文本编码
        test_text = "金融科技是指运用科技手段改进金融服务效率的新兴领域"
        
        start_time = time.time()
        embedding = rag_system.encode_text(test_text)
        encode_time = time.time() - start_time
        
        if embedding:
            logger.info(f"✓ 单个文本编码成功，耗时: {encode_time:.2f}秒，维度: {len(embedding)}")
        else:
            logger.error("✗ 单个文本编码失败")
            return False
        
        # 测试缓存效果
        start_time = time.time()
        embedding2 = rag_system.encode_text(test_text)  # 相同文本，应该命中缓存
        cache_time = time.time() - start_time
        
        logger.info(f"缓存命中耗时: {cache_time:.4f}秒 (应该很快)")
        
        # 测试批量编码
        test_texts = [
            "人工智能在金融领域的应用越来越广泛",
            "区块链技术为金融交易提供了新的可能性",
            "大数据分析帮助银行更好地了解客户需求",
            "移动支付改变了人们的消费习惯",
            "风险管理是金融机构的核心业务之一"
        ]
        
        start_time = time.time()
        batch_embeddings = rag_system.encode_texts_batch(test_texts, batch_size=3)
        batch_time = time.time() - start_time
        
        if len(batch_embeddings) == len(test_texts):
            avg_time = batch_time / len(test_texts)
            logger.info(f"✓ 批量编码成功，总耗时: {batch_time:.2f}秒，平均: {avg_time:.2f}秒/文本")
        else:
            logger.error("✗ 批量编码失败")
            return False
        
        # 显示性能统计
        cache_stats = rag_system.optimizer.get_performance_report()
        logger.info("性能统计:")
        for key, value in cache_stats.items():
            logger.info(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"RAG系统测试失败: {e}")
        return False


def test_batch_processor():
    """测试批量处理器"""
    logger.info("=== 测试批量处理器 ===")
    
    try:
        # 创建优化器和批量处理器
        optimizer = BGEPerformanceOptimizer()
        optimizer.setup_environment_optimization()
        
        # 初始化RAG系统以获取模型
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("无法初始化RAG系统")
            return False
        
        batch_processor = BGEBatchProcessor(optimizer)
        
        # 准备测试数据
        test_texts = [
            "金融科技创新推动行业发展",
            "数字货币的未来发展趋势",
            "银行业数字化转型的挑战与机遇",
            "保险科技如何改变传统保险业",
            "证券市场的智能化交易系统",
            "风险控制在金融科技中的重要性",
            "区块链在供应链金融中的应用",
            "人工智能助力信贷风险评估"
        ]
        
        # 测试批量处理
        start_time = time.time()
        results = batch_processor.process_batch_smart(
            test_texts, 
            rag_system.embedding_model, 
            batch_size=4
        )
        total_time = time.time() - start_time
        
        if len(results) == len(test_texts):
            avg_time = total_time / len(test_texts)
            logger.info(f"✓ 批量处理成功，总耗时: {total_time:.2f}秒，平均: {avg_time:.2f}秒/文本")
        else:
            logger.error("✗ 批量处理失败")
            return False
        
        # 测试缓存效果
        start_time = time.time()
        results2 = batch_processor.process_batch_smart(
            test_texts[:4],  # 重复前4个文本
            rag_system.embedding_model,
            batch_size=4
        )
        cache_test_time = time.time() - start_time
        
        logger.info(f"缓存测试耗时: {cache_test_time:.2f}秒 (应该很快)")
        
        # 显示性能报告
        performance_report = optimizer.get_performance_report()
        logger.info("批量处理性能报告:")
        for key, value in performance_report.items():
            logger.info(f"  {key}: {value}")
        
        # 显示优化建议
        suggestions = optimizer.suggest_optimizations()
        logger.info("优化建议:")
        for suggestion in suggestions:
            logger.info(f"  - {suggestion}")
        
        return True
        
    except Exception as e:
        logger.error(f"批量处理器测试失败: {e}")
        return False


def performance_comparison():
    """性能对比测试"""
    logger.info("=== 性能对比测试 ===")
    
    try:
        # 准备测试数据
        test_texts = [
            f"这是第{i}个测试文本，用于评估BGE-M3模型在CPU上的性能优化效果。"
            f"文本内容包含了金融、科技、人工智能等相关主题的描述。" * 2
            for i in range(20)
        ]
        
        # 初始化系统
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("系统初始化失败")
            return False
        
        # 测试1: 单个编码
        logger.info("测试单个编码性能...")
        start_time = time.time()
        for text in test_texts[:5]:
            rag_system.encode_text(text)
        single_time = time.time() - start_time
        logger.info(f"单个编码总耗时: {single_time:.2f}秒")
        
        # 清空缓存以确保公平比较
        rag_system.optimizer.clear_cache()
        
        # 测试2: 批量编码
        logger.info("测试批量编码性能...")
        start_time = time.time()
        rag_system.encode_texts_batch(test_texts[:5], batch_size=3)
        batch_time = time.time() - start_time
        logger.info(f"批量编码总耗时: {batch_time:.2f}秒")
        
        # 性能提升计算
        if single_time > 0:
            improvement = ((single_time - batch_time) / single_time) * 100
            logger.info(f"性能提升: {improvement:.1f}%")
        
        # 测试3: 缓存效果
        logger.info("测试缓存效果...")
        start_time = time.time()
        for text in test_texts[:5]:  # 重复编码相同文本
            rag_system.encode_text(text)
        cache_time = time.time() - start_time
        logger.info(f"缓存命中总耗时: {cache_time:.2f}秒")
        
        # 显示最终性能报告
        final_report = rag_system.optimizer.get_performance_report()
        logger.info("最终性能报告:")
        for key, value in final_report.items():
            logger.info(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"性能对比测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始BGE-M3 CPU优化测试")
    
    tests = [
        ("基本优化功能", test_basic_optimization),
        ("RAG系统优化", test_rag_system_optimization),
        ("批量处理器", test_batch_processor),
        ("性能对比", performance_comparison)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            if result:
                logger.info(f"✓ {test_name} 测试通过")
            else:
                logger.error(f"✗ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！BGE-M3 CPU优化功能正常")
    else:
        logger.warning(f"⚠️  有 {total - passed} 个测试失败，请检查相关功能")


if __name__ == "__main__":
    main()
