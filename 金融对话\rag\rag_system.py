"""
RAG检索系统
负责文档嵌入、相似度搜索和知识检索功能
优化BGE-M3模型在CPU上的运行效率
"""
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Optional
from loguru import logger
import numpy as np
import sys
from pathlib import Path
import gc
import psutil
import os
import threading
import queue
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.milvus_manager import MilvusManager
from idconfig.config import Config

class BGEOptimizer:
    """BGE-M3模型CPU优化器"""

    def __init__(self):
        self.text_cache = {}
        self.max_cache_size = 2000
        self.batch_queue = queue.Queue()
        self.processing_lock = threading.Lock()
        self.model_lock = threading.Lock()

    def setup_cpu_optimization(self):
        """设置CPU优化参数"""
        try:
            import torch
            import os

            # 设置CPU线程数
            cpu_count = os.cpu_count()
            optimal_threads = min(4, max(1, cpu_count // 2))
            torch.set_num_threads(optimal_threads)

            # 设置OpenMP线程数
            os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
            os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
            os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)

            # 禁用一些不必要的功能以提高性能
            torch.set_grad_enabled(False)

            logger.info(f"CPU优化设置完成 - 线程数: {optimal_threads}")
            return True

        except Exception as e:
            logger.error(f"CPU优化设置失败: {e}")
            return False

    def preprocess_text_optimized(self, text: str) -> str:
        """优化的文本预处理"""
        # 快速清理和标准化
        text = text.strip()
        if not text:
            return ""

        # 移除多余空白
        text = ' '.join(text.split())

        # 智能截断 - BGE-M3最优长度
        max_length = 512
        if len(text) > max_length:
            # 保留重要部分
            words = text.split()
            if len(words) > 100:
                # 保留前70%和后30%
                split_point = int(len(words) * 0.7)
                text = ' '.join(words[:split_point] + words[-int(len(words) * 0.3):])
            else:
                text = text[:max_length]

        return text

    def get_text_hash(self, text: str) -> str:
        """快速文本哈希"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def cache_embedding(self, text_hash: str, embedding: List[float]):
        """缓存嵌入向量"""
        if len(self.text_cache) >= self.max_cache_size:
            # 删除最旧的20%
            items_to_remove = list(self.text_cache.keys())[:self.max_cache_size // 5]
            for key in items_to_remove:
                del self.text_cache[key]

        self.text_cache[text_hash] = embedding

    def get_cached_embedding(self, text_hash: str) -> Optional[List[float]]:
        """获取缓存的嵌入向量"""
        return self.text_cache.get(text_hash)

class RAGSystem:
    def __init__(self):
        self.config = Config()
        self.embedding_model = None
        self.milvus_manager = MilvusManager()
        self.optimizer = BGEOptimizer()  # 使用优化器
        self._text_cache = {}  # 保持向后兼容
        self._max_cache_size = 1000
        
    def initialize(self):
        """初始化RAG系统"""
        try:
            # 首先设置CPU优化
            if not self.optimizer.setup_cpu_optimization():
                logger.warning("CPU优化设置失败，继续使用默认设置")

            # 初始化嵌入模型
            logger.info(f"加载嵌入模型: {self.config.EMBEDDING_MODEL}")

            import torch
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            logger.info(f"使用设备: {device}")

            # 加载模型时使用优化设置
            self.embedding_model = SentenceTransformer(
                self.config.EMBEDDING_MODEL,
                device=device,
                trust_remote_code=True
            )

            # CPU特定优化
            if device == 'cpu':
                # 设置为评估模式
                self.embedding_model.eval()

                # 禁用梯度计算
                for param in self.embedding_model.parameters():
                    param.requires_grad = False

                # 启用推理优化
                try:
                    # 尝试使用JIT编译优化（如果支持）
                    if hasattr(torch.jit, 'optimize_for_inference'):
                        for module in self.embedding_model.modules():
                            if hasattr(module, 'eval'):
                                module.eval()
                except Exception as e:
                    logger.debug(f"JIT优化不可用: {e}")

                logger.info("已启用增强CPU优化设置")

            logger.info("嵌入模型加载完成")

            # 验证模型配置
            if not self._validate_model_config():
                return False

            # 初始化Milvus管理器
            if not self.milvus_manager.initialize():
                logger.error("Milvus管理器初始化失败")
                return False

            logger.info("RAG系统初始化完成")
            return True

        except Exception as e:
            logger.error(f"RAG系统初始化失败: {e}")
            return False

    def _validate_model_config(self):
        """验证模型配置"""
        try:
            # 测试编码一个简单文本
            test_text = "测试文本"
            test_embedding = self.embedding_model.encode(test_text, normalize_embeddings=True)

            actual_dim = len(test_embedding)
            expected_dim = self.config.VECTOR_DIM

            logger.info(f"模型实际维度: {actual_dim}, 配置维度: {expected_dim}")

            # 检查是否为BGE-M3模型
            if "bge-m3" in self.config.EMBEDDING_MODEL.lower():
                if actual_dim != 1024:
                    logger.error(f"BGE-M3模型应该是1024维，但检测到{actual_dim}维")
                    return False
                if expected_dim != 1024:
                    logger.error("BGE-M3模型需要设置VECTOR_DIM=1024")
                    return False
                logger.info("✓ BGE-M3模型配置验证通过")

            # 检查向量是否归一化
            norm = np.linalg.norm(test_embedding)
            if abs(norm - 1.0) > 0.01:
                logger.warning(f"向量未完全归一化，norm={norm:.4f}")
            else:
                logger.info("✓ 向量归一化验证通过")

            return True

        except Exception as e:
            logger.error(f"模型配置验证失败: {e}")
            return False

    def _preprocess_text(self, text: str) -> str:
        """预处理文本以提高编码效率"""
        # 移除多余的空白字符
        text = ' '.join(text.split())

        # 限制文本长度以提高处理速度
        max_length = 512  # BGE-M3的最大输入长度
        if len(text) > max_length:
            # 智能截断：保留开头和结尾
            half_length = max_length // 2 - 50
            text = text[:half_length] + "..." + text[-half_length:]

        return text

    def _cache_embedding(self, text_hash: str, embedding: List[float]):
        """缓存嵌入向量"""
        # 如果缓存已满，删除最旧的条目
        if len(self._text_cache) >= self._max_cache_size:
            # 删除第一个条目（最旧的）
            oldest_key = next(iter(self._text_cache))
            del self._text_cache[oldest_key]

        self._text_cache[text_hash] = embedding

    def clear_cache(self):
        """清空缓存"""
        self._text_cache.clear()
        logger.info("已清空文本缓存")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self._text_cache),
            "max_cache_size": self._max_cache_size,
            "cache_usage": f"{len(self._text_cache)}/{self._max_cache_size}"
        }
    
    def encode_text(self, text: str) -> List[float]:
        """优化的文本编码方法"""
        if not text or not text.strip():
            return []

        # 使用优化器进行预处理和缓存检查
        processed_text = self.optimizer.preprocess_text_optimized(text)
        text_hash = self.optimizer.get_text_hash(processed_text)

        # 检查缓存
        cached_embedding = self.optimizer.get_cached_embedding(text_hash)
        if cached_embedding:
            return cached_embedding

        # 优化的编码过程
        try:
            if not self.embedding_model:
                logger.error("嵌入模型未初始化")
                return []

            start_time = time.time()

            # 使用模型锁确保线程安全
            with self.optimizer.model_lock:
                import torch
                with torch.no_grad():
                    # 优化的编码参数
                    embedding = self.embedding_model.encode(
                        processed_text,
                        normalize_embeddings=True,
                        batch_size=1,
                        show_progress_bar=False,
                        convert_to_numpy=True,
                        device=self.embedding_model.device
                    )

            encode_time = time.time() - start_time

            # 性能监控
            if encode_time > 2:  # 降低警告阈值
                logger.warning(f"编码耗时: {encode_time:.2f}秒，文本长度: {len(processed_text)}")
            elif encode_time < 0.5:
                logger.debug(f"编码高效: {encode_time:.2f}秒")

            # 验证向量
            if not self._validate_embedding(embedding):
                return []

            result = embedding.tolist()

            # 缓存结果
            self.optimizer.cache_embedding(text_hash, result)

            return result

        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            return []

    def _validate_embedding(self, embedding) -> bool:
        """验证嵌入向量的有效性"""
        try:
            # 检查维度
            actual_dim = len(embedding)
            expected_dim = self.config.VECTOR_DIM

            if actual_dim != expected_dim:
                logger.warning(f"向量维度不匹配: {actual_dim} != {expected_dim}")
                return False

            # 确保是numpy数组
            if not hasattr(embedding, 'dtype'):
                embedding = np.array(embedding, dtype=np.float32)

            # 检查有效性
            if np.any(np.isnan(embedding)) or np.any(np.isinf(embedding)):
                logger.error("向量包含无效值")
                return False

            return True

        except Exception as e:
            logger.error(f"向量验证失败: {e}")
            return False

    def encode_texts_batch(self, texts: List[str], batch_size: int = 4) -> List[List[float]]:
        """优化的批量编码方法"""
        if not texts:
            return []

        logger.info(f"开始优化批量编码 {len(texts)} 个文本，批次大小: {batch_size}")

        all_embeddings = []

        # 预处理和缓存检查
        texts_to_encode = []
        text_hashes = []
        cached_results = {}

        for text in texts:
            processed_text = self.optimizer.preprocess_text_optimized(text)
            text_hash = self.optimizer.get_text_hash(processed_text)
            text_hashes.append(text_hash)

            cached_embedding = self.optimizer.get_cached_embedding(text_hash)
            if cached_embedding:
                cached_results[text_hash] = cached_embedding
            else:
                texts_to_encode.append(processed_text)

        logger.info(f"缓存命中: {len(cached_results)}/{len(texts)}, 需要编码: {len(texts_to_encode)}")

        # 批量编码未缓存的文本
        new_embeddings = {}
        if texts_to_encode:
            new_embeddings = self._batch_encode_optimized(texts_to_encode, batch_size)

        # 组装最终结果
        for i, text_hash in enumerate(text_hashes):
            if text_hash in cached_results:
                all_embeddings.append(cached_results[text_hash])
            else:
                processed_text = self.optimizer.preprocess_text_optimized(texts[i])
                if processed_text in new_embeddings:
                    embedding = new_embeddings[processed_text]
                    all_embeddings.append(embedding)
                    # 缓存新的嵌入
                    self.optimizer.cache_embedding(text_hash, embedding)
                else:
                    # 回退到单个编码
                    embedding = self.encode_text(texts[i])
                    all_embeddings.append(embedding if embedding else [0.0] * self.config.VECTOR_DIM)

        logger.info(f"批量编码完成，共处理 {len(all_embeddings)} 个文本")
        return all_embeddings

    def _batch_encode_optimized(self, texts: List[str], batch_size: int) -> Dict[str, List[float]]:
        """优化的批量编码实现"""
        results = {}
        total_batches = (len(texts) + batch_size - 1) // batch_size

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1

            try:
                start_time = time.time()

                with self.optimizer.model_lock:
                    import torch
                    with torch.no_grad():
                        batch_embeddings = self.embedding_model.encode(
                            batch_texts,
                            normalize_embeddings=True,
                            batch_size=len(batch_texts),
                            show_progress_bar=False,
                            convert_to_numpy=True,
                            device=self.embedding_model.device
                        )

                # 存储结果
                for text, embedding in zip(batch_texts, batch_embeddings):
                    if self._validate_embedding(embedding):
                        results[text] = embedding.tolist()

                batch_time = time.time() - start_time
                avg_time = batch_time / len(batch_texts)
                logger.info(f"批次 {batch_num}/{total_batches} 完成: {batch_time:.2f}s, 平均: {avg_time:.2f}s/文本")

                # 内存管理
                if batch_num % 3 == 0:
                    gc.collect()

            except Exception as e:
                logger.error(f"批次 {batch_num} 编码失败: {e}")
                # 回退到单个编码
                for text in batch_texts:
                    try:
                        embedding = self.encode_text(text)
                        if embedding:
                            results[text] = embedding
                    except Exception as single_error:
                        logger.error(f"单个编码失败: {single_error}")

        return results

    def add_knowledge(self, content: str, category: str, source: str = ""):
        """添加知识到知识库"""
        try:
            # 生成嵌入向量
            embedding = self.encode_text(content)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "content": content,
                "category": category,
                "source": source,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_knowledge(data)
            
        except Exception as e:
            logger.error(f"添加知识失败: {e}")
            return False
    
    def add_conversation_history(self, session_id: str, user_query: str, 
                               assistant_response: str, timestamp: int):
        """添加对话历史"""
        try:
            # 将用户查询和助手回复组合作为嵌入内容
            combined_text = f"用户: {user_query}\n助手: {assistant_response}"
            
            # 生成嵌入向量
            embedding = self.encode_text(combined_text)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "session_id": session_id,
                "user_query": user_query,
                "assistant_response": assistant_response,
                "timestamp": timestamp,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_history(data)
            
        except Exception as e:
            logger.error(f"添加对话历史失败: {e}")
            return False
    
    def search_knowledge(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关知识"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索知识库
            results = self.milvus_manager.search_knowledge(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"知识库搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索知识失败: {e}")
            return []
    
    def search_conversation_history(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关对话历史"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索历史对话
            results = self.milvus_manager.search_history(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"历史对话搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索对话历史失败: {e}")
            return []
    
    def retrieve_context(self, query: str, include_images: bool = True) -> Dict[str, Any]:
        """检索相关上下文信息"""
        try:
            # 搜索知识库
            knowledge_results = self.search_knowledge(query)

            # 搜索历史对话
            history_results = self.search_conversation_history(query)

            # 搜索相关图片（如果启用）
            image_results = []
            if include_images:
                image_results = self.search_related_images(query)

            # 组织上下文信息
            context = {
                "knowledge": knowledge_results,
                "history": history_results,
                "images": image_results,
                "query": query
            }

            logger.info(f"上下文检索完成 - 知识: {len(knowledge_results)}条, 历史: {len(history_results)}条, 图片: {len(image_results)}张")
            return context

        except Exception as e:
            logger.error(f"检索上下文失败: {e}")
            return {"knowledge": [], "history": [], "images": [], "query": query}
    
    def batch_add_knowledge(self, knowledge_list: List[Dict[str, str]]):
        """优化的批量添加知识方法"""
        try:
            logger.info(f"开始优化批量添加 {len(knowledge_list)} 个知识块到向量数据库")

            total_count = len(knowledge_list)
            batch_size = 30  # 减小批次大小以提高稳定性
            processed_count = 0

            for i in range(0, total_count, batch_size):
                batch_items = knowledge_list[i:i + batch_size]
                batch_texts = [item["content"] for item in batch_items]

                try:
                    # 显示进度
                    progress = ((i + len(batch_items)) / total_count * 100)
                    logger.info(f"正在生成向量: {i + 1}-{i + len(batch_items)}/{total_count} ({progress:.1f}%)")

                    # 优化批量编码
                    batch_embeddings = self.encode_texts_batch(batch_texts, batch_size=min(4, len(batch_texts)))

                    # 准备数据
                    data = []
                    for item, embedding in zip(batch_items, batch_embeddings):
                        if embedding and len(embedding) > 0:
                            data.append({
                                "content": item["content"],
                                "category": item.get("category", "general"),
                                "source": item.get("source", ""),
                                "embedding": embedding
                            })
                        else:
                            logger.warning(f"跳过无法生成向量的知识块: {item.get('content', '')[:100]}...")

                    # 插入数据库
                    if data:
                        memory_percent = psutil.virtual_memory().percent
                        logger.info(f"插入批次数据到数据库: {len(data)} 个知识块 (内存使用: {memory_percent:.1f}%)")

                        if not self.milvus_manager.insert_knowledge(data):
                            logger.error(f"批次插入失败，已处理 {processed_count} 个知识块")
                            return False

                        processed_count += len(data)

                        # 内存管理
                        if memory_percent > 75:
                            logger.info("执行内存清理")
                            gc.collect()

                except Exception as e:
                    logger.error(f"处理批次 {i + 1}-{i + len(batch_items)} 时出错: {e}")
                    # 回退到单个处理
                    for item in batch_items:
                        try:
                            embedding = self.encode_text(item["content"])
                            if embedding:
                                single_data = [{
                                    "content": item["content"],
                                    "category": item.get("category", "general"),
                                    "source": item.get("source", ""),
                                    "embedding": embedding
                                }]
                                if self.milvus_manager.insert_knowledge(single_data):
                                    processed_count += 1
                        except Exception as single_error:
                            logger.error(f"单个处理失败: {single_error}")

            logger.info(f"成功完成批量添加，共处理 {processed_count}/{total_count} 个知识块")
            return processed_count > 0

        except Exception as e:
            logger.error(f"批量添加知识失败: {e}")
            return False

    def search_related_images(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """搜索与查询相关的图片"""
        try:
            # 导入多模态检索器
            from rag.multimodal_retrieval import MultimodalImageRetriever

            # 创建多模态检索器实例
            multimodal_retriever = MultimodalImageRetriever()
            if not multimodal_retriever.initialize():
                logger.warning("多模态检索器初始化失败，跳过图片搜索")
                return []

            # 搜索相关图片
            image_results = multimodal_retriever.search_images_by_text(query, top_k)

            # 过滤和格式化结果
            formatted_results = []
            for result in image_results:
                # 使用final_score作为主要评分标准
                score = result.get("final_score", result.get("similarity", 0))
                if score >= 0.2:  # 设置相似度阈值
                    formatted_result = {
                        "image_id": result.get("image_id"),
                        "pdf_name": result.get("pdf_name"),
                        "page_number": result.get("page_number"),
                        "image_index": result.get("image_index"),
                        "description": result.get("description"),
                        "image_type": result.get("image_type"),
                        "similarity": result.get("similarity", 0),
                        "final_score": score,
                        "width": result.get("width"),
                        "height": result.get("height")
                    }
                    formatted_results.append(formatted_result)

            logger.info(f"图片搜索完成，找到 {len(formatted_results)} 张相关图片")
            return formatted_results

        except ImportError:
            logger.warning("多模态功能不可用，跳过图片搜索")
            return []
        except Exception as e:
            logger.error(f"搜索相关图片失败: {e}")
            return []
